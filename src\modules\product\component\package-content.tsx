'use client'

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import {  PhotoSection } from './package-detail/photo-section'
import {  TripInfoSection } from './package-detail/trip-info-section'
import { HighlightSection } from './package-detail/highlight-section'
import { DescriptionSection } from './package-detail/description-section'
import { ShortItinerarySection } from './package-detail/short-itinerary-section'
import { IncludesSection } from './package-detail/include-section'
import { ExcludesSection } from './package-detail/exclude-section'
import { MapSection } from './package-detail/map-section'
import { VideoSection } from './package-detail/review-video'

import { 
  PackageContentData, 
  TripHighlightsContentProps 
} from '@/types/package-form'

export function TripHighlightsContent({ 
  activeHighlight, 
  packageData, 
  onContentChange 
}: TripHighlightsContentProps) {

  // Handle simple field changes (title, body)
  const handleFieldChange = (field: keyof PackageContentData, value: string) => {
    if (onContentChange && packageData) {
      onContentChange(field, value)
    }
  }

  // Handle array changes (photos, videos, etc.)
  const handleArrayChange = (field: keyof PackageContentData, value: any[]) => {
    if (onContentChange && packageData) {
      onContentChange(field, value)
    }
  }

  // Handle file changes
  const handleFileChange = (field: keyof PackageContentData, value: File | null) => {
    if (onContentChange && packageData) {
      onContentChange(field, value)
    }
  }

  // Highlights Section Handlers
  const handleHighlightsChange = (field: 'title' | 'body', value: string) => {
    if (field === 'title') {
      handleFieldChange('highlightsTitle', value)
    } else {
      handleFieldChange('highlightsBody', value)
    }
  }

  // Description Section Handlers
  const handleDescriptionChange = (field: 'title' | 'body', value: string) => {
    if (field === 'title') {
      handleFieldChange('descriptionTitle', value)
    } else {
      handleFieldChange('descriptionBody', value)
    }
  }

  // Short Itinerary Handlers
  const handleShortItineraryTitleChange = (value: string) => {
    handleFieldChange('shortItineraryTitle', value)
  }

//   const handleShortItineraryItemsChange = (items: string[]) => {
//     handleArrayChange('shortItineraryItems', items)
//   }

  const addShortItineraryItem = () => {
    if (packageData) {
      const newItems = [...packageData.shortItineraryItems, ""]
      handleArrayChange('shortItineraryItems', newItems)
    }
  }

  const removeShortItineraryItem = (idx: number) => {
    if (packageData) {
      const newItems = packageData.shortItineraryItems.filter((_, i) => i !== idx)
      handleArrayChange('shortItineraryItems', newItems)
    }
  }

  const updateShortItineraryItem = (idx: number, text: string) => {
    if (packageData) {
      const newItems = [...packageData.shortItineraryItems]
      newItems[idx] = text
      handleArrayChange('shortItineraryItems', newItems)
    }
  }

  // Photo Section Handlers
  const handlePhotoTitleChange = (value: string) => {
    handleFieldChange('photoTitle', value)
  }

  const addPhoto = () => {
    if (packageData) {
      const newPhotos = [...packageData.photos, { file: null, caption: "" }]
      handleArrayChange('photos', newPhotos)
    }
  }

  const removePhoto = (idx: number) => {
    if (packageData) {
      const newPhotos = packageData.photos.filter((_, i) => i !== idx)
      handleArrayChange('photos', newPhotos)
    }
  }

  const updatePhotoFile = (idx: number, file: File | null) => {
    if (packageData) {
      const newPhotos = [...packageData.photos]
      newPhotos[idx] = { ...newPhotos[idx], file }
      handleArrayChange('photos', newPhotos)
    }
  }

  const updatePhotoCaption = (idx: number, caption: string) => {
    if (packageData) {
      const newPhotos = [...packageData.photos]
      newPhotos[idx] = { ...newPhotos[idx], caption }
      handleArrayChange('photos', newPhotos)
    }
  }

  // Video Section Handlers
  const handleVideoTitleChange = (value: string) => {
    handleFieldChange('videoTitle', value)
  }

  const addYoutubeLink = () => {
    if (packageData) {
      const newLinks = [...packageData.youtubeLinks, ""]
      handleArrayChange('youtubeLinks', newLinks)
    }
  }

  const removeYoutubeLink = (idx: number) => {
    if (packageData) {
      const newLinks = packageData.youtubeLinks.filter((_, i) => i !== idx)
      handleArrayChange('youtubeLinks', newLinks)
    }
  }

  const updateYoutubeLink = (idx: number, url: string) => {
    if (packageData) {
      const newLinks = [...packageData.youtubeLinks]
      newLinks[idx] = url
      handleArrayChange('youtubeLinks', newLinks)
    }
  }

  // Includes Section Handlers
  const handleIncludesChange = (field: 'title' | 'body', value: string) => {
    if (field === 'title') {
      handleFieldChange('includesTitle', value)
    } else {
      handleFieldChange('includesBody', value)
    }
  }

  // Excludes Section Handlers
  const handleExcludesChange = (field: 'title' | 'body', value: string) => {
    if (field === 'title') {
      handleFieldChange('excludesTitle', value)
    } else {
      handleFieldChange('excludesBody', value)
    }
  }

  // Map Section Handlers
  const handleMapTitleChange = (value: string) => {
    handleFieldChange('mapTitle', value)
  }

  const handleMapFileChange = (file: File | null) => {
    handleFileChange('mapFile', file)
  }

  // Trip Info Section Handlers
  const handleTripInfoTitleChange = (value: string) => {
    handleFieldChange('tripInfoTitle', value)
  }

  const addTripInfo = () => {
    if (packageData) {
      const newInfos = [...packageData.tripInfos, { title: "", body: "", note: "" }]
      handleArrayChange('tripInfos', newInfos)
    }
  }

  const removeTripInfo = (idx: number) => {
    if (packageData) {
      const newInfos = packageData.tripInfos.filter((_, i) => i !== idx)
      handleArrayChange('tripInfos', newInfos)
    }
  }

  const updateTripInfo = (idx: number, field: "title" | "body" | "note", value: string) => {
    if (packageData) {
      const newInfos = [...packageData.tripInfos]
      newInfos[idx] = { ...newInfos[idx], [field]: value }
      handleArrayChange('tripInfos', newInfos)
    }
  }

  // If no package data is provided, show a message
  if (!packageData) {
    return (
      <Card>
        <CardContent className="space-y-6 pt-6">
          <p className="text-center text-gray-500 py-8">
            Loading package content...
          </p>
        </CardContent>
      </Card>
    )
  }

  const renderSection = () => {
    switch (activeHighlight) {
      case 'highlights':
        return (
          <HighlightSection 
            title={packageData.highlightsTitle}
            onTitleChange={(value) => handleHighlightsChange('title', value)}
            body={packageData.highlightsBody}
            onBodyChange={(value) => handleHighlightsChange('body', value)}
          />
        )
        
      case 'description':
        return (
          <DescriptionSection 
            title={packageData.descriptionTitle}
            onTitleChange={(value) => handleDescriptionChange('title', value)}
            body={packageData.descriptionBody}
            onBodyChange={(value) => handleDescriptionChange('body', value)}
          />
        )
        
      case 'shortItinerary':
        return (
          <ShortItinerarySection
            title={packageData.shortItineraryTitle}
            onTitleChange={handleShortItineraryTitleChange}
            items={packageData.shortItineraryItems}
            onAddItem={addShortItineraryItem}
            onUpdateItem={updateShortItineraryItem}
            onRemoveItem={removeShortItineraryItem}
          />
        )
        
      case 'photo':
        return (
          <PhotoSection 
            title={packageData.photoTitle}
            onTitleChange={handlePhotoTitleChange}
            photos={packageData.photos}
            onAddPhoto={addPhoto}
            onRemovePhoto={removePhoto}
            onUpdatePhotoFile={updatePhotoFile}
            onUpdatePhotoCaption={updatePhotoCaption}
          />
        )
        
      case 'video':
        return (
          <VideoSection
            title={packageData.videoTitle}
            onTitleChange={handleVideoTitleChange}
            links={packageData.youtubeLinks}
            onAddLink={addYoutubeLink}
            onUpdateLink={updateYoutubeLink}
            onRemoveLink={removeYoutubeLink}
          />
        )
        
      case 'includes':
        return (
          <IncludesSection 
            title={packageData.includesTitle}
            onTitleChange={(value) => handleIncludesChange('title', value)}
            body={packageData.includesBody}
            onBodyChange={(value) => handleIncludesChange('body', value)}
          />
        )
        
      case 'excludes':
        return (
          <ExcludesSection 
            title={packageData.excludesTitle}
            onTitleChange={(value) => handleExcludesChange('title', value)}
            body={packageData.excludesBody}
            onBodyChange={(value) => handleExcludesChange('body', value)}
          />
        )
        
      case 'map':
        return (
          <MapSection 
            title={packageData.mapTitle}
            onTitleChange={handleMapTitleChange}
            onFileChange={handleMapFileChange}
          />
        )
        
      case 'tripInfo':
        return (
          <TripInfoSection 
            title={packageData.tripInfoTitle}
            onTitleChange={handleTripInfoTitleChange}
            infos={packageData.tripInfos}
            onAddInfo={addTripInfo}
            onRemoveInfo={removeTripInfo}
            onUpdateInfo={updateTripInfo}
          />
        )
        
      default:
        return (
          <p className="text-center text-gray-500 py-8">
            Select a section from the sidebar to begin editing.
          </p>
        )
    }
  }

  return (
    <Card>
      <CardContent className="space-y-6 pt-6">
        {renderSection()}
      </CardContent>
    </Card>
  )
}



// 'use client'
// import React, { useState } from 'react'
// import { Card, CardContent } from "@/components/ui/card"
// import { PhotoEntry, PhotoSection } from './package-detail/photo-section'
// import { InfoEntry, TripInfoSection } from './package-detail/trip-info-section'
// import { HighlightSection } from './package-detail/highlight-section'
// import { DescriptionSection } from './package-detail/description-section'
// import { ShortItinerarySection } from './package-detail/short-itinerary-section'
// import { IncludesSection } from './package-detail/include-section'
// import { ExcludesSection } from './package-detail/exclude-section'
// import { MapSection } from './package-detail/map-section'
// import { VideoSection } from './package-detail/review-video'



// interface TripHighlightsContentProps {
//     activeHighlight: string
// }

// export function TripHighlightsContent({ activeHighlight }: TripHighlightsContentProps) {
//     const [highTitle, setHighTitle] = useState("8 Highlights of the Khopra Ridge Trek")
//     const [highBody, setHighBody] = useState("• Mountain views during the trek...")
//     const [descTitle, setDescTitle] = useState("Is the Khopra Ridge trek worth it?")
//     const [descBody, setDescBody] = useState("It's no wonder this question...")
//     const [shortItineraryItems, setShortItineraryItems] = useState<string[]>(["Day 1: Arrival..."])
//     const [shortItineraryTitle, setShortItineraryTitle] = useState("Short Itinerary")

//     const [photoTitle, setPhotoTitle] = useState("Photo Gallery")
//     const [photos, setPhotos] = useState<PhotoEntry[]>([])
//     const [videoTitle, setVideoTitle] = useState("Watch Our Trek Video")
//     const [youtubeLinks, setYoutubeLinks] = useState<string[]>([""])
//     const [incTitle, setIncTitle] = useState("WHAT'S INCLUDED")
//     const [incBody, setIncBody] = useState("• All permits and fees.")
//     const [exclTitle, setExclTitle] = useState("WHAT'S EXCLUDED")
//     const [exclBody, setExclBody] = useState("• International airfare.")
//     const [mapTitle, setMapTitle] = useState("Route Map")
//     const [, setMapFile] = useState<File | null>(null)
//     const [tripInfoTitle, setTripInfoTitle] = useState("Package Information")
//     const [tripInfos, setTripInfos] = useState<InfoEntry[]>([])

//     const addPhoto = () => setPhotos(p => [...p, { file: null, caption: "" }])
//     const removePhoto = (idx: number) => setPhotos(p => p.filter((_, i) => i !== idx))
//     const updatePhotoFile = (idx: number, file: File | null) => setPhotos(p => { const n = [...p]; n[idx].file = file; return n; })
//     const updatePhotoCaption = (idx: number, caption: string) => setPhotos(p => { const n = [...p]; n[idx].caption = caption; return n; })

//     const addYoutubeLink = () => setYoutubeLinks(links => [...links, ""])
//     const removeYoutubeLink = (idx: number) => setYoutubeLinks(links => links.filter((_, i) => i !== idx))
//     const updateYoutubeLink = (idx: number, url: string) => {
//         setYoutubeLinks(links => {
//             const newLinks = [...links]
//             newLinks[idx] = url
//             return newLinks
//         })
//     }

//     const addItineraryItem = () => setShortItineraryItems(items => [...items, ""])
//     const removeItineraryItem = (idx: number) => setShortItineraryItems(items => items.filter((_, i) => i !== idx))
//     const updateItineraryItem = (idx: number, text: string) => {
//         setShortItineraryItems(items => {
//             const newItems = [...items]
//             newItems[idx] = text
//             return newItems
//         })
//     }

//     const addTripInfo = () => setTripInfos(p => [...p, { title: "", body: "", note: "" }])
//     const removeTripInfo = (idx: number) => setTripInfos(p => p.filter((_, i) => i !== idx))
//     const updateTripInfo = (idx: number, field: "title" | "body" | "note", value: string) => {
//         setTripInfos(p => { const n = [...p]; n[idx] = { ...n[idx], [field]: value }; return n; })
//     }

//     const renderSection = () => {
//         switch (activeHighlight) {
//             case 'highlights':
//                 return <HighlightSection title={highTitle} onTitleChange={setHighTitle} body={highBody} onBodyChange={setHighBody} />
//             case 'description':
//                 return <DescriptionSection title={descTitle} onTitleChange={setDescTitle} body={descBody} onBodyChange={setDescBody} />
//             case 'shortItinerary':
//                 return <ShortItinerarySection
//                     title={shortItineraryTitle}
//                     onTitleChange={setShortItineraryTitle}
//                     items={shortItineraryItems}
//                     onAddItem={addItineraryItem}
//                     onUpdateItem={updateItineraryItem}
//                     onRemoveItem={removeItineraryItem}
//                 />
//             case 'photo':
//                 return <PhotoSection title={photoTitle} onTitleChange={setPhotoTitle} photos={photos} onAddPhoto={addPhoto} onRemovePhoto={removePhoto} onUpdatePhotoFile={updatePhotoFile} onUpdatePhotoCaption={updatePhotoCaption} />
//             case 'video':
//                 return <VideoSection
//                     title={videoTitle}
//                     onTitleChange={setVideoTitle}
//                     links={youtubeLinks}
//                     onAddLink={addYoutubeLink}
//                     onUpdateLink={updateYoutubeLink}
//                     onRemoveLink={removeYoutubeLink}
//                 />
//             case 'includes':
//                 return <IncludesSection title={incTitle} onTitleChange={setIncTitle} body={incBody} onBodyChange={setIncBody} />
//             case 'excludes':
//                 return <ExcludesSection title={exclTitle} onTitleChange={setExclTitle} body={exclBody} onBodyChange={setExclBody} />
//             case 'map':
//                 return <MapSection title={mapTitle} onTitleChange={setMapTitle} onFileChange={setMapFile} />
//             case 'tripInfo':
//                 return <TripInfoSection title={tripInfoTitle} onTitleChange={setTripInfoTitle} infos={tripInfos} onAddInfo={addTripInfo} onRemoveInfo={removeTripInfo} onUpdateInfo={updateTripInfo} />
//             default:
//                 return <p className="text-center text-gray-500 py-8">Select a section from the sidebar to begin editing.</p>
//         }
//     }

//     return (
//         <Card>
//             <CardContent className="space-y-6 pt-6">{renderSection()}</CardContent>
//         </Card>
//     )
// }