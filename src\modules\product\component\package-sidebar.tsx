// /modules/product/component/PackageSidebar.tsx
'use client'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface TripHighlightsSidebarProps {
  activeHighlight: string
  onHighlightChange: (highlight: string) => void
}

export function PackageSidebar({ activeHighlight, onHighlightChange }: TripHighlightsSidebarProps) {
  const highlights = [
    { id: 'highlights', label: 'Trip Highlights' },
    { id: 'description', label: 'Description' },
    { id: 'shortItinerary', label: 'Short Itinerary' },
    { id: 'photo', label: 'Photo' },
    { id: 'video', label: 'Video' },
    { id: 'includes', label: 'Includes' },
    { id: 'excludes', label: 'Excludes' },
    { id: 'map', label: 'Map' },
    { id: 'tripInfo', label: 'Trip Info' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {highlights.map((highlight) => (
          <Button
            key={highlight.id}
            variant={activeHighlight === highlight.id ? 'secondary' : 'ghost'}
            className="w-full justify-start"
            onClick={() => onHighlightChange(highlight.id)}
          >
            {highlight.label}
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}

// 'use client'

// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// interface TripHighlightsSidebarProps {
//   activeHighlight: string
//   onHighlightChange: (highlight: string) => void
// }

// export function PackageSidebar({ activeHighlight, onHighlightChange }: TripHighlightsSidebarProps) {
//   const highlights = [
//     { id: 'highlights', label: 'Trip Highlights' },
//     { id: 'description', label: 'Description' },
//     { id: 'shortItinerary', label: 'Short Itinerary' },
//     { id: 'photo', label: 'Photo' },
//     { id: 'video', label: 'Video' },
//     { id: 'includes', label: 'Includes' },
//     { id: 'excludes', label: 'Excludes' },
//     { id: 'map', label: 'Map' },
//     { id: 'tripInfo', label: 'Trip Info' },
//   ]

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Package Details</CardTitle>
//       </CardHeader>
//       <CardContent className="space-y-2">
//         {highlights.map((highlight) => (
//           <Button
//             key={highlight.id}
//             variant={activeHighlight === highlight.id ? 'default' : 'ghost'}
//             className={`w-full justify-start ${
//               highlight.id === 'description' && activeHighlight === highlight.id
//                 ? 'bg-cyan-500 text-white hover:bg-cyan-600'
//                 : ''
//             }`}
//             onClick={() => onHighlightChange(highlight.id)}
//           >
//             {highlight.label}
//           </Button>
//         ))}
//       </CardContent>
//     </Card>
//   )
// }
